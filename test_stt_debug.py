#!/usr/bin/env python3
"""
Debug script to test STT processing and memory storage
"""
import asyncio
import os
import sys
from dotenv import load_dotenv

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2
from agents.stt.stt_agent import STTAgent
from core.memory.memory_manager import MemoryManager

load_dotenv()

async def test_stt_processing():
    """Test STT processing with a sample audio file"""
    print("🔍 Testing STT Processing and Memory Storage")
    print("=" * 50)
    
    # Create a test session
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'test_stt_user'
    
    try:
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")
        
        # Get memory manager
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]
        
        # Test 1: Check if we can create an STT agent
        print("\n📝 Test 1: Creating STT Agent")
        stt_agent = STTAgent(session_id=session_id, state_id="stt")
        print("✅ STT Agent created successfully")
        
        # Test 2: Check if audio file exists (use a test file if available)
        print("\n📝 Test 2: Checking for test audio file")
        test_audio_files = [
            "last_recorded.wav",
            "temp_audio_*.wav",
            "data/filler_words/user_conversation_part_1.mp3"
        ]
        
        audio_file = None
        for file_pattern in test_audio_files:
            if "*" in file_pattern:
                import glob
                matches = glob.glob(file_pattern)
                if matches:
                    audio_file = matches[0]
                    break
            elif os.path.exists(file_pattern):
                audio_file = file_pattern
                break
        
        if not audio_file:
            print("⚠️  No test audio file found. Creating a dummy file for testing...")
            # Create a minimal WAV file for testing
            import wave
            import numpy as np
            
            # Generate 1 second of silence at 16kHz
            sample_rate = 16000
            duration = 1.0
            samples = np.zeros(int(sample_rate * duration), dtype=np.int16)
            
            audio_file = "test_silence.wav"
            with wave.open(audio_file, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(sample_rate)
                wf.writeframes(samples.tobytes())
            print(f"✅ Created test audio file: {audio_file}")
        else:
            print(f"✅ Found test audio file: {audio_file}")
        
        # Test 3: Process audio with STT agent
        print("\n📝 Test 3: Processing audio with STT agent")
        try:
            result = await stt_agent.process(audio_file, {"session_id": session_id})
            print(f"✅ STT processing result: {result.status}")
            print(f"   📄 Transcript: '{result.outputs.get('text', 'NO TEXT')}'")
            print(f"   ⏱️  Latency: {result.outputs.get('latencySTT', 'NO LATENCY')}ms")
        except Exception as e:
            print(f"❌ STT processing failed: {e}")
            return
        
        # Test 4: Check memory storage
        print("\n📝 Test 4: Checking memory storage")
        
        # Check what keys are stored in contextual memory
        all_keys = await memory_manager.get_all_contextual()
        print(f"📋 All contextual memory keys: {list(all_keys.keys())}")
        
        # Check for the specific transcript key
        workflow_state = "Inquiry"  # This is what the logs show
        transcript_key = f"{session_id}_{workflow_state}_text"
        transcript_value = await memory_manager.get("contextual", transcript_key)
        print(f"🔍 Looking for transcript key: {transcript_key}")
        print(f"📄 Transcript value: {transcript_value}")
        
        # Test 5: Simulate StateManager storage pattern
        print("\n📝 Test 5: Simulating StateManager storage pattern")
        
        # Store the transcript the way StateManager would
        if result.outputs.get('text'):
            await memory_manager.set("contextual", transcript_key, result.outputs['text'])
            print(f"✅ Stored transcript with key: {transcript_key}")
            
            # Verify retrieval
            retrieved = await memory_manager.get("contextual", transcript_key)
            print(f"✅ Retrieved transcript: '{retrieved}'")
        
        # Test 6: Test preprocessing input construction
        print("\n📝 Test 6: Testing preprocessing input construction")
        
        # Simulate how StateManager constructs preprocessing input
        process_input_data = {}
        preprocessing_input_config = {"transcript": "text"}  # From workflow config
        
        for key, value in preprocessing_input_config.items():
            memory_key = f"{session_id}_{workflow_state}_{value}"
            saved_val = await memory_manager.get("contextual", memory_key)
            if saved_val:
                process_input_data[key] = saved_val
                print(f"✅ Found {key}: '{saved_val}' from key: {memory_key}")
            else:
                print(f"❌ Missing {key} from key: {memory_key}")
        
        print(f"📋 Final preprocessing input: {process_input_data}")
        
        # Clean up test file
        if audio_file == "test_silence.wav":
            try:
                os.unlink(audio_file)
            except:
                pass
        
    finally:
        await session_manager.cleanup_session(session_id, reason="stt_debug_test_complete")
        print("\n✅ Session cleaned up")

if __name__ == "__main__":
    asyncio.run(test_stt_processing())
